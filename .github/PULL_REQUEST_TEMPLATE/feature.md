<ins>**Feature Description**</ins>

<ins>**Modifications (what changed)**</ins>

<ins>**Additions (what's new)**</ins>

<ins>**Depends on**</ins>

<ins>**Checklist**</ins>

Check the boxes ("[x]") that apply, remove the list items that don't apply.

- [ ] Uses Existing CF patterns
- [ ] Applies role / permission / feature checks
- [ ] Implements and tested for accessibility / WCAG best practices
- [ ] Uses correct language strings (see document below for more info)
- [ ] Adheres to company content style guide (see https://creativeforce.atlassian.net/l/c/h7C002Bw)
- [ ] Ensures custom theme colours override defaults
- [ ] Ensures sensitive information isn't logged (see https://creativeforce.atlassian.net/wiki/x/DYCjhg)
- [ ] Created new / updated existing unit tests and they pass
- [ ] Created new / updated existing feature tests and they pass
- [ ] Created new / updated existing JavaScript tests and they pass
- [ ] Test suite passes
- [ ] Deployed to and tested on staging
- [ ] QA notified of changes, breaking changes and any refactoring
- [ ] API docs updated
- [ ] Solution documented on Confluence or shared via Tech talk or #minitechtalk

If adding a new resource, have you:
- [ ] Updated the https://github.com/tectonic/database-steriliser repo
- [ ] Updated the Destroy Account process

For more information check out: https://creativeforce.atlassian.net/l/c/bq17Sm3G
