<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\File\UploadRequest;
use AwardForce\Library\Filesystem\Metadata;
use AwardForce\Modules\Files\Commands\ProcessFileCommand;
use AwardForce\Modules\Files\Commands\UpdateFileMetadata;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Repositories\EloquentFileRepository;
use AwardForce\Modules\Files\Values\Size;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class FileControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private FileRepository|m\MockInterface $files;

    public function init()
    {
        \Bus::fake();
        app()->instance(FileRepository::class, $this->files = m::mock(FileRepository::class));
    }

    public function testItReturnsMetadataWithSizeAndModalObfuscatedString()
    {
        $file = new File;
        $file->metadata = new Metadata(size: $size = new Size(300, 300, 72));
        $this->files->shouldReceive('getById')
            ->with($fileId = 9890)
            ->andReturn($file)
            ->once();

        $controller = app(FileController::class);

        $metadata = $controller->metadata($fileId);

        $this->assertArrayHasKey('size', $metadata);
        $this->assertEquals((string) $size, $metadata['size']);
        $this->assertArrayHasKey('dataContent', $metadata);
        $this->assertIsString($metadata['dataContent']);
        $this->assertNotEmpty($metadata['dataContent']);

        \Bus::assertNotDispatched(UpdateFileMetadata::class);
    }

    public function testItUpdatesMetadataWhenIsMissing()
    {
        \Bus::shouldReceive('dispatchSync')
            ->with(m::type(UpdateFileMetadata::class), null)
            ->once()
            ->andReturn(new Metadata(size: $size = new Size(300, 300, 72)));
        $file = new File;
        $this->files->shouldReceive('getById')
            ->with($fileId = 9891)
            ->andReturn($file)
            ->once();

        $controller = app(FileController::class);

        $metadata = $controller->metadata($fileId);

        $this->assertEquals((string) $size, $metadata['size']);
    }

    #[TestWith(['mime', 'mime'])]
    #[TestWith([null, ''])]
    public function testItSetsMimeTypeFromRequest($requestMime, $expectedMime): void
    {
        $request = UploadRequest::create('', 'POST', parameters: [
            'original' => 'original',
            'name' => 'name',
            'resource' => 'resource',
            'resourceId' => 0,
            'foreignId' => 0,
            'language' => 'en',
            'mime' => $requestMime,
        ]);

        $controller = app(FileController::class);
        $response = $controller->upload($request);
        $fileId = $response['file'];

        // revert mock in order to query the db
        $eloquentFileRepository = app()->instance(FileRepository::class, app(EloquentFileRepository::class));
        $file = $eloquentFileRepository->getById($fileId);

        $this->assertInstanceOf(File::class, $file);
        $this->assertSame($file->mime, $expectedMime);

        \Bus::shouldReceive('dispatch')
            ->with(m::type(ProcessFileCommand::class));
    }
}
