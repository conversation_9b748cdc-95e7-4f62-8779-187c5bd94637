<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Controllers\Payment\PaymentRedirect;
use AwardForce\Http\Middleware\SessionPostParameters;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartRepository;
use AwardForce\Modules\Ecommerce\Cart\Services\LocksPayment;
use AwardForce\Modules\Payments\Commands\CancelPaymentCommand;
use AwardForce\Modules\Payments\Commands\CompletePaymentCommand;
use AwardForce\Modules\Payments\Requests\WebhookRequest;
use AwardForce\Modules\Payments\Services\PaymentService;
use Exception;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Platform\Http\Controller;

class PaymentController extends Controller
{
    use DispatchesJobs;
    use PaymentRedirect;

    public function __construct(private SessionPostParameters $sessionPostParameters)
    {
    }

    /**
     * Handle completing payment from off-site payment gateway
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function gatewayReturn(Request $request, Cart $cart)
    {
        try {
            $this->log($request);
            $gatewayParameters = $this->sessionPostParameters->retrieve($request);
            $response = $this->dispatchSync(new CompletePaymentCommand($request->gateway, $cart, $gatewayParameters));
        } catch (Exception $e) {
            return redirect()->route('cart.checkout')->withErrors($e->getMessage());
        } finally {
            LocksPayment::create(current_account_id(), consumer_id())->releaseLock();
        }

        if (! $response->success()) {
            return redirect()->route('cart.view')->withErrors($response->message());
        }

        return $this->successRedirect($cart);
    }

    public function webhook(WebhookRequest $request, CartRepository $carts)
    {
        $this->dispatch(new CompletePaymentCommand($request->gateway, $carts->forSlug($request->cartSlug()), $request->all()));

        return response()->json();
    }

    /**
     * Display pending payment page
     *
     * @return mixed
     */
    public function pending(Cart $cart, PaymentService $payments)
    {
        $gateway = $payments->getGateway($cart->getPaymentMethod());

        return $this->respond('payment.pending', compact('gateway'));
    }

    /**
     * Return the payment status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function status(Cart $cart, PaymentService $payments)
    {
        $gateway = $payments->getGateway($cart->getPaymentMethod());

        return response()->json(['status' => $gateway->status()]);
    }

    /**
     * Cancel payment from off-site payment gateway
     */
    public function gatewayCancel(Request $request, Cart $cart)
    {
        LocksPayment::create(current_account_id(), consumer_id())->releaseLock();

        $this->dispatch(new CancelPaymentCommand($request->gateway, $cart));

        return redirect()->route('cart.checkout');
    }

    protected function log(Request $request)
    {
        $slug = current_account()->slug;
        Log::info("[Account slug: {$slug}] {$request->gateway} payment gateway response: ".json_encode($request->all()));
    }
}
