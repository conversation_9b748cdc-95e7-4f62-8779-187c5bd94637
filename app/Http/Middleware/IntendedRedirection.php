<?php

namespace AwardForce\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class IntendedRedirection
{
    public function handle(Request $request, \Closure $next)
    {
        if ($request->filled('redirect') && $this->allowedRedirection($request->get('redirect'))) {
            session()->put('intended', $request->get('redirect'));
        }

        return $next($request);
    }

    private function allowedRedirection(string $redirect): bool
    {
        if (Str::startsWith($redirect, '/')) {
            return true;
        }

        $host = Arr::get(parse_url(urldecode($redirect)), 'host');
        $availableDomains = current_account()->domains->just('domain');
        if (app()->runningUnitTests()) {
            $availableDomains[] = Arr::get(parse_url(config('app.url')), 'host');
        }

        return Str::startsWith($host, $availableDomains);
    }
}
