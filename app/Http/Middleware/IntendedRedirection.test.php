<?php

namespace AwardForce\Http\Middleware;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class IntendedRedirectionTest extends BaseTestCase
{
    use Laravel;

    #[TestWith(['javaScript:void(0)'])]
    #[TestWith(['data:text/plain;base64,SGVsbG8sIFdvcmxkIQ=='])]
    #[TestWith(['other.domain.test'])]
    #[TestWith(['https://another.domain.test'])]
    public function testItDoesNotSetIntendedUrlForInvalidRedirect(string $redirect): void
    {
        CurrentAccount::shouldReceive('get')
            ->andSet('domains', collect([
                ['domain' => 'valid.domain'],
                ['domain' => 'other-valid.domain'],
            ]))
            ->andReturnSelf();

        $request = app(Request::class);
        $request->merge(['redirect' => $redirect]);

        $middleware = new IntendedRedirection();
        $middleware->handle($request, fn() => null);

        $this->assertFalse(session()->has('intended'));
    }

    #[TestWith(['/relative/path'])]
    #[TestWith(['https://valid.domain/absolute/path'])]
    #[TestWith(['https://other-valid.domain/another/path'])]
    public function testItSetsIntendedUrlForValidRedirect(string $redirect): void
    {
        CurrentAccount::shouldReceive('get')
            ->andSet('domains', collect([
                ['domain' => 'valid.domain'],
                ['domain' => 'other-valid.domain'],
            ]))
            ->andReturnSelf();

        $request = app(Request::class);
        $request->merge(['redirect' => $redirect]);
        $middleware = new IntendedRedirection();
        $middleware->handle($request, fn() => null);

        $this->assertTrue(session()->has('intended'));
        $this->assertEquals($redirect, session('intended'));
    }
}
