<?php

namespace AwardForce\Http\Requests\File;

use AwardForce\Modules\Files\Models\File;
use Tests\Library\Request;

final class UploadRequestTest extends Request
{
    protected function getRequestClass(): string
    {
        return UploadRequest::class;
    }

    public function testRequired(): void
    {
        $this->assertFails(
            [],
            ['name', 'original', 'resource']
        );

        $this->assertPasses(
            ['name' => 1, 'resource' => File::RESOURCE_ENTRIES],
            ['name', 'resource']
        );
    }

    public function testResource(): void
    {
        $this->assertPasses(
            [
                'resource' => File::RESOURCE_ENTRIES,
            ]
        );

        $this->assertFails(
            [
                'resource' => 'unknown',
            ]
        );

        $id = 5;

        $this->assertFails(
            [
                'resource' => File::RESOURCE_FILE_FIELD."-{$id}",
            ]
        );

        $this->assertFails(
            [
                'resource' => File::RESOURCE_FILE_FIELD."-{$id}",
                'resourceId' => 6,
            ],
            ['resource']
        );

        $this->assertPasses(
            [
                'resource' => File::RESOURCE_FILE_FIELD."-{$id}",
                'resourceId' => $id,
            ],
            ['resource']
        );

        $this->assertPasses(
            [
                'resource' => File::RESOURCE_FILE_FIELD."-{$id}",
                'resourceId' => $id,
                'foreignId' => 1,
            ]
        );
    }

    public function testItAllowsNullOrStringMime(): void
    {
        $this->assertPasses(['mime' => \Str::random()]);
        $this->assertPasses(['mime' => null]);

        $this->assertFails(['mime' => random_int(1, 1000)]);
        $this->assertFails(['mime' => true]);
    }
}
