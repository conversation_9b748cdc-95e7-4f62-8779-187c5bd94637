<?php

namespace AwardForce\Library\Identifier;

use AwardForce\Library\Identifier\Snowflake\SnowflakeFactory;
use AwardForce\Library\Identifier\Snowflake\Snowflakes;
use AwardForce\Library\ServiceProvider;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Database\Schema\Blueprint;

class IdentifierServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->registerSnowflakeFactory();
    }

    public function boot(): void
    {
        $this->registerBlueprintMacros();
    }

    private function registerSnowflakeFactory(): void
    {
        $this->app->singleton(SnowflakeFactory::class, function ($app) {
            $config = $app['config']->get('awardforce.identifier');

            if (! is_null($config['pod_id'])) {
                // Ensure worker_id and process_id are derived from pod_id.
                // Binary AND with 0x1F (binary 11111) to limit the range to 0-31 (picking the lowest 5 bits).
                $config['worker_id'] = crc32($config['pod_id']) & 0x1F;
                $config['process_id'] = crc32($config['pod_id'].getmypid()) & 0x1F;
            }

            return new Snowflakes($config['worker_id'], $config['process_id']);
        });
    }

    public function provides(): array
    {
        return [
            SnowflakeFactory::class,
        ];
    }

    public function registerBlueprintMacros(): void
    {
        Blueprint::macro('snowflake', function (string $column) {
            return $this->unsignedBigInteger($column);
        });

        Blueprint::macro('snowflakeId', function (string $column = 'id') {
            return $this->unsignedBigInteger($column)->primary();
        });
    }
}
