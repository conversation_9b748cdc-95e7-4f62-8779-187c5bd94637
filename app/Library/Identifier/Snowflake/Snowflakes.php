<?php

namespace AwardForce\Library\Identifier\Snowflake;

use Brick\Math\BigInteger;
use DateTimeInterface;
use Psr\Clock\ClockInterface;
use Ramsey\Identifier\Exception\InvalidArgument;
use Ramsey\Identifier\Service\Clock\ClockSequence;
use Ramsey\Identifier\Service\Clock\MonotonicClockSequence;
use Ramsey\Identifier\Service\Clock\Precision;
use Ramsey\Identifier\Service\Clock\SystemClock;
use Ramsey\Identifier\Snowflake\Internal\StandardFactory;

/**
 * Based on \Ramsey\Identifier\Snowflake\DiscordSnowflakeFactory
 */
class Snowflakes implements \Ramsey\Identifier\SnowflakeFactory
{
    use StandardFactory;

    private const int TIMESTAMP_BIT_SHIFTS = 22;

    /**
     * For performance, we'll prepare the worker and process Id bits and store
     * them for repeated use.
     */
    private readonly int $workerProcessIdShifted;

    /**
     * We increase this value each time our clock sequence rolls over and add the value to the milliseconds to ensure
     * the values are monotonically increasing.
     */
    private int $clockSequenceCounter = 0;

    /**
     * Constructs a factory for creating TheForce Snowflakes
     *
     * @param  int  $workerId A worker identifier to use when creating Snowflakes; we take the modulo of this integer
     *     divided by 32, giving it an effective range of 0-31 (i.e., 5 bits).
     * @param  int  $processId A process identifier to use when creating Snowflakes; we take the modulo of this integer
     *     divided by 32, giving it an effective range of 0-31 (i.e., 5 bits).
     * @param  Clock  $clock A clock used to provide a date-time instance; defaults to {@see SystemClock}.
     * @param  ClockSequence  $sequence A clock sequence value to prevent collisions; defaults to {@see MonotonicClockSequence}.
     */
    public function __construct(
        private readonly int $workerId,
        private readonly int $processId,
        private readonly ClockInterface $clock = new SystemClock(),
        private readonly ClockSequence $sequence = new MonotonicClockSequence,
    ) {
        $this->workerProcessIdShifted = ($this->workerId & 0x1F) << 17 | ($this->processId & 0x1F) << 12;
    }

    /**
     * @throws InvalidArgument
     */
    public function create(): SnowflakeId
    {
        return $this->createFromDateTime($this->clock->now());
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromBytes(string $identifier): SnowflakeId
    {
        return new SnowflakeId($this->convertFromBytes($identifier));
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromDateTime(DateTimeInterface $dateTime): SnowflakeId
    {
        $milliseconds = (int) $dateTime->format(Precision::Millisecond->value) - Epoch::TheForce->value;

        if ($milliseconds < 0) {
            throw new InvalidArgument(sprintf(
                'Timestamp may not be earlier than the TheForce epoch, %s',
                Epoch::TheForce->toIso8601(),
            ));
        }

        if ($milliseconds > 0x3FFFFFFFFFF) {
            throw new InvalidArgument(
                'Snowflakes cannot have a date-time greater than 2164-07-15T07:35:11.103Z',
            );
        }

        // Use modular arithmetic to roll over the sequence value at mod 0x1000 (4096).
        $sequence = $this->sequence->next((string) ($this->workerId + $this->processId), $dateTime) % 0x1000;

        // Increase the milliseconds by the current value of the clock sequence counter.
        $milliseconds += $this->clockSequenceCounter;
        $millisecondsShifted = $milliseconds << self::TIMESTAMP_BIT_SHIFTS;

        // If the sequence is currently 0x0fff (4095), bump the clock sequence counter, since we're rolling over.
        if ($sequence === 0x0FFF) {
            $this->clockSequenceCounter++;
        }

        if ($millisecondsShifted > $milliseconds) {
            /** @var int<0, max> $identifier */
            $identifier = $millisecondsShifted | $this->workerProcessIdShifted | $sequence;
        } else {
            /** @var numeric-string $identifier */
            $identifier = (string) BigInteger::of($milliseconds)
                ->shiftedLeft(self::TIMESTAMP_BIT_SHIFTS)
                ->or($this->workerProcessIdShifted)
                ->or($sequence);
        }

        return new SnowflakeId($identifier);
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromHexadecimal(string $identifier): SnowflakeId
    {
        return new SnowflakeId($this->convertFromHexadecimal($identifier));
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromInteger(int|string $identifier): SnowflakeId
    {
        return new SnowflakeId($identifier);
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromString(string $identifier): SnowflakeId
    {
        return new SnowflakeId($identifier);
    }
}
