<?php

namespace AwardForce\Library\Search\Actions;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Features\Facades\Feature as FeatureFacade;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Features\Feature;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

abstract class SimpleActionTestBase extends BaseTestCase
{
    use Database;
    use Laravel;

    public function init()
    {
        $this->programManager = $this->setupUserWithRole('Program manager');
        $this->guest = $this->setupUserWithRole('Guest');
        \Consumer::set(new UserConsumer($this->programManager));
    }

    public function testApplies(): void
    {
        $this->setOwner();

        $this->assertTrue($this->action()->applies($this->record()));
    }

    public function notApplies()
    {
        $this->setInValidRole();
        $this->assertFalse($this->action()->applies());
    }

    public function testRender(): void
    {
        $record = $this->record();
        $rendered = $this->action()->render($record);

        $this->assertStringContainsStringIgnoringCase((string) $record->id, $rendered);
        $this->assertStringContainsStringIgnoringCase('JSON.parse', $rendered);
    }

    #[TestWith(['custom_roles', 'enabled', true, true])]
    #[TestWith(['custom_roles', 'enabled', false, false])]
    #[TestWith(['custom_roles', 'disabled', true, false])]
    #[TestWith(['custom_roles', 'disabled', false, true])]
    public function testAppliesWithFeature(string $featureName, string $featureEnabled, bool $systemEnabled, bool $expectedResult)
    {
        FeatureFacade::shouldReceive('enabled')
            ->with($featureName)
            ->andReturn($systemEnabled);

        $this->setOwner();

        $action = $this->action()->feature(new Feature($featureName, $featureEnabled));

        $this->assertEquals($expectedResult, $action->applies($this->record()));
    }

    protected function setValidRole()
    {
        \Consumer::set(new UserConsumer($this->programManager));
    }

    protected function setOwner(): void
    {
        $this->setValidRole();
        current_account()->setOwner($this->programManager);
    }

    protected function setInValidRole()
    {
        \Consumer::set(new UserConsumer($this->guest));
    }

    abstract public function action(): ActionOverflowAction;

    abstract public function record();
}
