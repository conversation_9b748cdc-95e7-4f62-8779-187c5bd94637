<?php

namespace AwardForce\Modules\AIAgents\Domain\Services;

use AwardForce\Modules\AIAgents\Boundary\Composite;
use AwardForce\Modules\AIAgents\Boundary\Context;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\AIAgents\Domain\Exceptions\CompositeNotFoundException;
use Illuminate\Container\Attributes\Tag;
use Illuminate\Support\Collection;
use Webmozart\Assert\Assert;

class ContextResolver
{
    public const string COMPOSITE = 'ai-agent-composite';
    public const string CONTEXT = 'ai-agent-context';

    public function __construct(
        #[Tag(self::COMPOSITE)] private readonly iterable $composites,
        #[Tag(self::CONTEXT)] private readonly iterable $contexts,
    ) {
        Assert::allIsInstanceOf($composites, Composite::class);
        Assert::allIsInstanceOf($contexts, Context::class);
    }

    public function generateFor(Resource $resource, array $requiredContexts): PromptContext
    {
        $metadata = $this->composite($resource->type)->metadata($resource->id);

        return $this->matchingContexts($requiredContexts)
            ->reduce(
                fn(PromptContext $promptContext, Context $context) => $context->handle($promptContext, $metadata),
                new PromptContext
            );
    }

    public function composite(ResourceType $resourceType): Composite
    {
        return once(
            fn() => collect($this->composites)
                ->first(fn(Composite $composite) => $composite->supports($resourceType))
                ?? throw CompositeNotFoundException::for($resourceType)
        );
    }

    /**
     * Filters contexts based on the required contexts.
     *
     * @param  string[]  $requiredContexts
     * @return Collection<int, Context>
     */
    private function matchingContexts(array $requiredContexts): Collection
    {
        return collect($this->contexts)
            ->filter(static fn(Context $context) => $context->applies($requiredContexts));
    }
}
