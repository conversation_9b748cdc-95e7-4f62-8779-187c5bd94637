<?php

namespace AwardForce\Modules\AIAgents\Domain\Services;

use AwardForce\Modules\AIAgents\Boundary\Composite;
use AwardForce\Modules\AIAgents\Boundary\Context;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\AIAgents\Domain\Exceptions\CompositeNotFoundException;
use Mockery;
use Mockery\MockInterface;
use Tests\BaseTestCase;

final class ContextResolverTest extends BaseTestCase
{
    private ContextResolver $resolver;
    private Composite|MockInterface $supportingComposite;
    private Composite|MockInterface $nonSupportingComposite;
    private Context|MockInterface $context1;
    private Context|MockInterface $context2;

    protected function init(): void
    {
        $this->supportingComposite = Mockery::mock(Composite::class);
        $this->nonSupportingComposite = Mockery::mock(Composite::class);
        $this->context1 = Mockery::mock(Context::class);
        $this->context2 = Mockery::mock(Context::class);

        $this->resolver = new ContextResolver(
            [$this->supportingComposite, $this->nonSupportingComposite],
            [$this->context1, $this->context2]
        );
    }

    public function testGenerateForFindsMatchingCompositeAndGeneratesContext(): void
    {
        $resourceType = ResourceType::Entry;
        $resourceId = 123;
        $resource = new Resource($resourceType, $resourceId);
        $requiredContexts = ['test1', 'test2'];
        $metadata = ['id' => $resourceId, 'foo' => 'bar'];

        $this->supportingComposite
            ->shouldReceive('supports')
            ->once()
            ->with($resourceType)
            ->andReturnTrue();

        $this->supportingComposite
            ->shouldReceive('metadata')
            ->once()
            ->with($resourceId)
            ->andReturn($metadata);

        $this->context1
            ->shouldReceive('applies')
            ->once()
            ->with($requiredContexts)
            ->andReturnTrue();

        $this->context2
            ->shouldReceive('applies')
            ->once()
            ->with($requiredContexts)
            ->andReturnFalse();

        $expectedPromptContext = new PromptContext(['processed' => 'data']);

        $this->context1
            ->shouldReceive('handle')
            ->once()
            ->with(Mockery::type(PromptContext::class), $metadata)
            ->andReturn($expectedPromptContext);

        $result = $this->resolver->generateFor($resource, $requiredContexts);

        $this->assertSame($expectedPromptContext, $result);
    }

    public function testGenerateForWithNoMatchingContextsReturnsEmptyPromptContext(): void
    {
        $resourceType = ResourceType::Entry;
        $resourceId = 123;
        $resource = new Resource($resourceType, $resourceId);
        $requiredContexts = ['test1', 'test2'];
        $metadata = ['id' => $resourceId];

        $this->supportingComposite
            ->shouldReceive('supports')
            ->once()
            ->with($resourceType)
            ->andReturnTrue();

        $this->supportingComposite
            ->shouldReceive('metadata')
            ->once()
            ->with($resourceId)
            ->andReturn($metadata);

        $this->context1
            ->shouldReceive('applies')
            ->once()
            ->with($requiredContexts)
            ->andReturnFalse();

        $this->context2
            ->shouldReceive('applies')
            ->once()
            ->with($requiredContexts)
            ->andReturnFalse();

        $result = $this->resolver->generateFor($resource, $requiredContexts);

        $this->assertInstanceOf(PromptContext::class, $result);
        $this->assertEmpty($result->getAttributes());
    }

    public function testGenerateForThrowsExceptionWhenNoMatchingCompositesFound(): void
    {
        $resourceType = ResourceType::Entry;
        $resourceId = 123;
        $resource = new Resource($resourceType, $resourceId);
        $requiredContexts = ['test1', 'test2'];

        $this->supportingComposite
            ->shouldReceive('supports')
            ->once()
            ->with($resourceType)
            ->andReturnFalse();

        $this->nonSupportingComposite
            ->shouldReceive('supports')
            ->once()
            ->with($resourceType)
            ->andReturnFalse();

        $this->expectException(CompositeNotFoundException::class);
        $this->expectExceptionMessage("No composite found for resource type: {$resourceType->name}");

        $this->resolver->generateFor($resource, $requiredContexts);
    }

    public function testResolveCompositeReturnsMatchingComposite(): void
    {
        $resourceType = ResourceType::Entry;

        $this->supportingComposite
            ->shouldReceive('supports')
            ->once()
            ->with($resourceType)
            ->andReturnTrue();

        $result = $this->resolver->composite($resourceType);

        $this->assertSame($this->supportingComposite, $result);
    }
}
