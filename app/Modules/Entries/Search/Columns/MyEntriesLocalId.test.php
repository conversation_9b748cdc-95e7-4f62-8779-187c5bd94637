<?php

namespace AwardForce\Modules\Entries\Search\Columns;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use DOMDocument;
use HtmlString;
use Mockery as m;
use Str;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MyEntriesLocalIdTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private m\MockInterface&MyEntriesLocalId $entriesMock;

    protected function init()
    {
        $this->entriesMock = m::mock(MyEntriesLocalId::class)->makePartial();
    }

    public function testHtmlShowsIdWhenDisplayIdEnabled(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['displayId' => true])]);
        $entry = $this->muffin(Entry::class, ['formId' => $form->id]);
        $randomString = Str::random();

        $this->entriesMock->shouldReceive('value')
            ->once()
            ->with($entry)
            ->andReturn($randomString);

        $value = $this->entriesMock->html($entry);

        $this->assertInstanceOf(HtmlString::class, $value);
        $doc = tap(new DOMDocument(), fn($d) => $d->loadHTML($value));
        $this->assertSame($randomString, $doc->getElementsByTagName('a')->item(0)->textContent);
    }

    public function testHtmlDoesNotShowIdWhenDisplayIdDisabled(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['displayId' => false])]);
        $entry = $this->muffin(Entry::class, ['formId' => $form->id]);
        $this->entriesMock->shouldNotReceive('value');

        $value = $this->entriesMock->html($entry);

        $this->assertInstanceOf(HtmlString::class, $value);
        $doc = tap(new DOMDocument(), fn($d) => $d->loadHTML($value));
        $this->assertSame('', $doc->getElementsByTagName('a')->item(0)->textContent);
    }
}
