<?php

namespace AwardForce\Modules\Funding;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\AIAgents\Domain\Services\ContextResolver;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Data\EloquentAllocationRepository;
use AwardForce\Modules\Funding\Data\EloquentFundRepository;
use AwardForce\Modules\Funding\Data\FundRepository;
use AwardForce\Modules\Funding\Events\AllocationWasDeleted;
use AwardForce\Modules\Funding\Events\FundWasCopied;
use AwardForce\Modules\Funding\Events\FundWasUpdated;
use AwardForce\Modules\Funding\Events\RecalculateFundsAvailable;
use AwardForce\Modules\Funding\Services\AIAgents\AllocationContext;
use Platform\Localisation\Listeners\CopyTranslationsListener;

class FundingServiceProvider extends ServiceProvider
{
    /**
     * @var bool
     */
    public $defer = true;

    /**
     * The repository bindings for the Entries module.
     *
     * @var array
     */
    protected $repositories = [
        FundRepository::class => EloquentFundRepository::class,
        AllocationRepository::class => EloquentAllocationRepository::class,
    ];

    protected $listeners = [
        AllocationWasDeleted::class => RecalculateFundsAvailable::class.'@whenAllocationWasDeleted',
        FundWasCopied::class => CopyTranslationsListener::class.'@whenModelWasCopied',
        FundWasUpdated::class => RecalculateFundsAvailable::class.'@whenFundWasUpdated',
        'active.filters' => [
            Events\ActiveFiltersListener::class.'@handle',
        ],
    ];

    public function register()
    {
        parent::register();

        $this->app->tag(AllocationContext::class, ContextResolver::CONTEXT);
    }
}
