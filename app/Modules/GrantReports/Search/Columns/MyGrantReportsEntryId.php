<?php

namespace AwardForce\Modules\GrantReports\Search\Columns;

use Platform\Search\Defaults;

class MyGrantReportsEntryId extends EntryId
{
    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function html($record): string
    {
        if ($record->displayId()) {
            return $this->value($record);
        }

        return '';
    }
}
