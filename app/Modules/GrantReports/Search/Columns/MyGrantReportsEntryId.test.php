<?php

namespace AwardForce\Modules\GrantReports\Search\Columns;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class MyGrantReportsEntryIdTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItDisplaysTheLocalEntryID(): void
    {
        $form = FormSelector::get();
        $form->settings = FormSettings::create(['displayId' => true]);
        $form->save();
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => ($entry = $this->muffin(Entry::class))->id]);

        $value = (new MyGrantReportsEntryId)->html($grantReport);

        $this->assertSame(local_id($entry), $value);
    }
}
