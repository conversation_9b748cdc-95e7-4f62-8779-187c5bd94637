<?php

namespace AwardForce\Modules\GrantReports\Services\AIAgent;

use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Domain\Exceptions\MissingParameterException;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class GrantReportContextTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItSupportsGrantReportContextType()
    {
        $this->assertTrue(app(GrantReportContext::class)->applies([AIFieldContext::GrantReports->value]));
    }

    public function testItRequiresEntryId()
    {
        $this->expectException(MissingParameterException::class);

        app(GrantReportContext::class)
            ->handle(new PromptContext(), []);
    }

    public function testItProvidesFormData()
    {
        $form = $this->muffin(Form::class);

        $entry = $this->muffin(Entry::class);

        $grantReport = $this->muffin(GrantReport::class, [
            'form_id' => $form->id,
            'entry_id' => $entry->id,
        ]);

        $actual = app(GrantReportContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertEquals($grantReport->form->name, $actual->get('grant_reports.0.form.name'));
    }

    public function testItProvidesTabData()
    {
        $form = $this->muffin(Form::class);

        $tab = $this->muffin(Tab::class, ['form_id' => $form->id]);
        $tab->saveTranslation('en_GB', 'name', 'Test Tab', current_account_id());
        $tab->save();

        $field = $this->muffin(Field::class, [
            'form_id' => $form->id,
            'tab_id' => $tab->id,
            'type' => 'text',
        ]);
        $field->saveTranslation('en_GB', 'label', 'Test Field', current_account_id());
        $field->save();

        $entry = $this->muffin(Entry::class);

        $grantReport = $this->muffin(GrantReport::class, [
            'form_id' => $form->id,
            'entry_id' => $entry->id,
            'values' => [
                (string) $field->slug => 'Test Value',
            ],
        ]);

        $actual = app(GrantReportContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertEquals([
            'name' => 'Test Tab',
            'type' => $tab->type,
            'fields' => [
                [
                    'label' => 'Test Field',
                    'type' => $field->type,
                    'value' => 'Test Value',
                ],
            ],
        ], $actual->get('grant_reports.0.tabs.0'));
    }
}
