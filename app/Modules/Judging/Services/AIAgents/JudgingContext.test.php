<?php

namespace AwardForce\Modules\Judging\Services\AIAgents;

use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Domain\Exceptions\MissingParameterException;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Judging\Data\Decision;
use AwardForce\Modules\Judging\Data\Score;
use AwardForce\Modules\Judging\Services\ConsensusKeeper;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class JudgingContextTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItSupportsJudgingContext(): void
    {
        $this->assertTrue(app(JudgingContext::class)->applies([AIFieldContext::Judging->value]));
    }

    public function testItRequiresEntryId(): void
    {
        $this->expectException(MissingParameterException::class);

        app(JudgingContext::class)
            ->handle(new PromptContext(), []);
    }

    /**
     * @runInSeparateProcess
     */
    public function testItProvidesScoreSetData(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_VIP,
        ]);
        $scoreSet->saveTranslation('en_GB', 'name', 'VIP Judging', current_account_id());
        $scoreSet->save();

        $entry = $this->muffin(Entry::class);
        $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
        ]);

        $actual = app(JudgingContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertEquals($scoreSet->name, $actual->get('judging.0.score_set.name'));
        $this->assertEquals($scoreSet->mode, $actual->get('judging.0.score_set.mode'));
    }

    public function testItProvidesDataForVipJudging(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_VIP,
        ]);
        $scoreSet->save();

        [$criterion1, $criterion2] = $this->muffins(2, ScoringCriterion::class, [
            'scoreset_id' => $scoreSet->id,
            'maximum_score' => 10,
            'minimum_score' => 0,
            'weight' => 1,
        ]);

        $entry = $this->muffin(Entry::class);

        $assignment1 = $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
        ]);
        $assignment2 = $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
        ]);
        $ignoredAssignment = $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $this->muffin(ScoreSet::class)->id,
            'status' => Assignment::STATUS_IN_PROGRESS,
        ]);

        $this->muffin(Score::class, [
            'assignment_id' => $assignment1->id,
            'scoring_criterion_id' => $criterion1->id,
            'score' => 5,
        ]);
        $this->muffin(Score::class, [
            'assignment_id' => $assignment1->id,
            'scoring_criterion_id' => $criterion2->id,
            'score' => 7,
        ]);

        $this->muffin(Score::class, [
            'assignment_id' => $assignment2->id,
            'scoring_criterion_id' => $criterion1->id,
            'score' => 3,
        ]);
        $this->muffin(Score::class, [
            'assignment_id' => $assignment2->id,
            'scoring_criterion_id' => $criterion2->id,
            'score' => 9,
        ]);

        $actual = app(JudgingContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertCount(1, $actual['judging']);

        $this->assertEquals(12, $actual->get('judging.0.score_set.scores.total_score.value'));
        $this->assertEquals(20, $actual->get('judging.0.score_set.scores.total_score.max_score'));
        $this->assertEquals('12/20', $actual->get('judging.0.score_set.scores.total_score.final_score'));

        $this->assertCount(2, $actual->get('judging.0.score_set.scores.criteria'));
        $this->assertEquals($criterion1->title, $actual->get('judging.0.score_set.scores.criteria.0.name'));
        $this->assertEquals(4, $actual->get('judging.0.score_set.scores.criteria.0.value'));
        $this->assertEquals(10, $actual->get('judging.0.score_set.scores.criteria.0.max_score'));
        $this->assertEquals('4/10', $actual->get('judging.0.score_set.scores.criteria.0.final_score'));
        $this->assertEquals($criterion1->weight, $actual->get('judging.0.score_set.scores.criteria.0.weight'));
    }

    public function testItProvidesDataForVoting(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_VOTING,
        ]);
        $scoreSet->save();

        $entry = $this->muffin(Entry::class);

        $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
            'total_votes' => 10,
        ]);
        $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
            'total_votes' => 5,
        ]);
        $ignoredAssignment = $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $this->muffin(ScoreSet::class)->id,
            'status' => Assignment::STATUS_IN_PROGRESS,
            'total_votes' => 3,
        ]);

        $actual = app(JudgingContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertCount(1, $actual->get('judging'));
        $this->assertEquals(15, $actual->get('judging.0.score_set.scores.total_votes'));
    }

    public function testItProvidesDataForTopPick(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_TOP_PICK,
            'top_pick_preferences' => 2,
        ]);
        $scoreSet->save();

        $entry = $this->muffin(Entry::class);

        $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
            'top_pick_preference' => 1,
        ]);
        $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
            'top_pick_preference' => 2,
        ]);
        $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
            'top_pick_preference' => 1,
        ]);
        $ignoredAssignment = $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $this->muffin(ScoreSet::class)->id,
            'status' => Assignment::STATUS_IN_PROGRESS,
            'top_pick_preference' => 2,
        ]);

        $actual = app(JudgingContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertCount(1, $actual['judging']);
        $this->assertEquals(
            [
                'preference_1' => 2,
                'preference_2' => 1,
            ],
            $actual->get('judging.0.score_set.scores')
        );
    }

    public function testItProvidesQualifyingData(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_QUALIFYING,
            'min_responses_for_consensus' => 1,
            'min_percentage_to_qualify' => 50,
        ]);
        $scoreSet->save();

        $entry = $this->muffin(Entry::class);

        $assignment = $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'score_set_id' => $scoreSet->id,
            'status' => Assignment::STATUS_COMPLETE,
        ]);
        $this->muffin(Decision::class, [
            'assignment_id' => $assignment->id,
            'decision' => Decision::DECISION_PASS,
        ]);

        $actual = app(JudgingContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertCount(1, $actual['judging']);
        $this->assertEquals(
            ConsensusKeeper::CONSENSUS_QUALIFIED,
            $actual->get('judging.0.score_set.scores.final_decision'),
        );
    }
}
