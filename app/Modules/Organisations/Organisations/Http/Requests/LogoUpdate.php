<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Requests;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;

class LogoUpdate extends FormRequest
{
    public function authorize()
    {
        return \Consumer::isOwnerOrProgramManager() || $this->organisation()->mine();
    }

    private function organisation(): Organisation
    {
        return app(OrganisationProjectionRepository::class)
            ->fields(['id', 'administrator_id'])
            ->primary($this->organisation)
            ->first();
    }
}
