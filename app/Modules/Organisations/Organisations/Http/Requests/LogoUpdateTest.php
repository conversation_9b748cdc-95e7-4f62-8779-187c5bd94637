<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Requests;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\EloquentOrganisationProjectionRepository;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class LogoUpdateTest extends BaseTestCase
{
    use Laravel;

    private EloquentOrganisationProjectionRepository|m\MockInterface $organisations;

    public function init()
    {
        $this->organisations = m::mock(EloquentOrganisationProjectionRepository::class);
        $this->organisations->shouldReceive('fields')->andReturnSelf();
        $this->organisations->shouldReceive('primary')->andReturnSelf();
        $organisation = new Organisation;
        $organisation->administrator_id = 3;
        $this->organisations->shouldReceive('first')->andReturn($organisation);
        app()->instance(EloquentOrganisationProjectionRepository::class, $this->organisations);
        $account = new Account();

        CurrentAccount::set($account);
    }

    public function testShouldAuthorizeWhenConsumerIsOrganisationAdministrator()
    {
        $request = new LogoUpdate;
        $request->merge([
            'organisation' => *********,
        ]);

        \Consumer::shouldReceive('isOwnerOrProgramManager')->andReturn(false)->times(1);
        \Consumer::shouldReceive('id')->once()->andReturn($adminstratorId = 3);

        $organisation = new Organisation;
        $organisation->administrator_id = $adminstratorId;

        $this->assertTrue($request->authorize());
    }

    public function testShouldAuthorizeWhenConsumerIsOwnerOrManager()
    {
        $request = new LogoUpdate;
        $request->merge([
            'organisation' => *********,
        ]);

        \Consumer::shouldReceive('id')->times(0); // This way we ensure that the consumer ID is not checked and it went with lazy check on owner or manager
        \Consumer::shouldReceive('isOwnerOrProgramManager')->once()->andReturn(true);
        $this->assertTrue($request->authorize());
    }

    public function testShouldNotAuthorizeWhenConsumerIsNotOwnerManagerOrAdministrator()
    {
        $request = new LogoUpdate;
        $request->merge([
            'organisation' => *********,
        ]);

        \Consumer::shouldReceive('id')->once()->andReturn(331);
        \Consumer::shouldReceive('isOwnerOrProgramManager')->once()->andReturn(false);
        $this->assertFalse($request->authorize());
    }
}
