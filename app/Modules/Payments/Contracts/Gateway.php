<?php

namespace AwardForce\Modules\Payments\Contracts;

use Eloquence\Behaviours\Slug;

interface Gateway
{
    /**
     * Get test mode
     *
     * @return bool
     */
    public function getTestMode();

    /**
     * Set test mode
     *
     *
     * @return void
     */
    public function setTestMode($mode);

    /**
     * Get currency code
     *
     * @return string
     */
    public function getCurrency();

    /**
     * Set the currency code
     *
     * @param  string  $currency
     * @return void
     */
    public function setCurrency($currency);

    /**
     * <PERSON>le making the purchase
     *
     * @param  array  $data
     * @return \AwardForce\Modules\Payments\Contracts\Response
     */
    public function purchase($amount, $data = []);

    /**
     * Extracts a cart slug from the request in the specific format provided by this gateway
     */
    public function cart(array $requestData): ?Slug;

    /**
     * Validate that the request received from the gateway is legitimate
     */
    public function validate(string $payload, array $headers): bool;
}
