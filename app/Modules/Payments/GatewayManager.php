<?php

namespace AwardForce\Modules\Payments;

use AwardForce\Modules\Payments\Contracts\Gateway;
use AwardForce\Modules\Payments\Exceptions\InvalidGatewayConfigException;
use AwardForce\Modules\Payments\Exceptions\InvalidGatewayException;
use AwardForce\Modules\Payments\Gateways\Alipay;
use AwardForce\Modules\Payments\Gateways\CCAvenue;
use AwardForce\Modules\Payments\Gateways\CommBankBpoint;
use AwardForce\Modules\Payments\Gateways\Cybersource;
use AwardForce\Modules\Payments\Gateways\Ideal;
use AwardForce\Modules\Payments\Gateways\Invoice;
use AwardForce\Modules\Payments\Gateways\NABTransact;
use AwardForce\Modules\Payments\Gateways\OmnipayAuthorizeNet;
use AwardForce\Modules\Payments\Gateways\OmnipayBluepay;
use AwardForce\Modules\Payments\Gateways\OmnipayEway;
use AwardForce\Modules\Payments\Gateways\OmnipayEwayRedirect;
use AwardForce\Modules\Payments\Gateways\OmnipayMercanet;
use AwardForce\Modules\Payments\Gateways\OmnipayPayflow;
use AwardForce\Modules\Payments\Gateways\OmnipayPaypalExpress;
use AwardForce\Modules\Payments\Gateways\OmnipayRealex;
use AwardForce\Modules\Payments\Gateways\OmnipaySagepay;
use AwardForce\Modules\Payments\Gateways\OmnipaySecurepay;
use AwardForce\Modules\Payments\Gateways\OmnipayStripeConnect;
use AwardForce\Modules\Payments\Gateways\OmnipayWorldpay;
use AwardForce\Modules\Payments\Gateways\OptimalNetbanx;
use AwardForce\Modules\Payments\Gateways\PayStack;
use AwardForce\Modules\Payments\Gateways\StripeCheckout;
use AwardForce\Modules\Payments\Gateways\WestpacPayway;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Contracts\Filesystem\Factory;

class GatewayManager
{
    /**
     * @var array
     */
    protected $settings;

    /**
     * @var Factory
     */
    protected $filesystem;

    public function __construct(SettingRepository $settingRepository, Factory $filesystem)
    {
        $this->settings = $settingRepository->getAllAsKeyValue();
        $this->filesystem = $filesystem;
    }

    /**
     * Create a new payment gateway object based name
     *
     * @param  string  $name
     * @return Gateway
     */
    public static function create($name)
    {
        $gatewayManager = app(self::class);

        $method = 'create'.studly_case($name).'Gateway';

        // We'll check to see if a creator method exists for the given driver.
        if (method_exists($gatewayManager, $method)) {
            return $gatewayManager->$method();
        }

        throw new InvalidGatewayException($name);
    }

    /**
     * Create a new AuthorizeNET payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipayAuthorizeNet
     */
    protected function createAuthorizenetGateway()
    {
        return new OmnipayAuthorizeNet(
            $this->settings['authorize-net-api-login-id'],
            $this->settings['authorize-net-transaction-key'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new BluePay payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipayBluepay
     */
    protected function createBluepayGateway()
    {
        return new OmnipayBluepay(
            $this->settings['bluepay-account-id'],
            $this->settings['bluepay-secret-key'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Bpoint payment gateway
     *
     * @return CommBankBpoint
     */
    protected function createBpointGateway()
    {
        return new CommBankBpoint(
            $this->settings['bpoint-api-username'],
            $this->settings['bpoint-api-password'],
            $this->settings['bpoint-merchant-number'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Cybersource payment gateway
     *
     * @return \AwardForce\Modules\Payments\Contracts\Gateway
     */
    protected function createCybersourceGateway()
    {
        return new Cybersource(
            $this->settings['cybersource-merchant-id'],
            $this->settings['cybersource-transaction-key'],
            $this->settings['cybersource-org-unit-id'],
            $this->settings['cybersource-api-key'],
            $this->settings['cybersource-api-identifier'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Eway payment gateway
     *
     * @return OmnipayEway
     */
    protected function createEwayGateway()
    {
        return new OmnipayEway(
            $this->settings['eway-api-key'],
            $this->settings['eway-password'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Eway (redirect) payment gateway
     */
    protected function createEwayRedirectGateway()
    {
        return new OmnipayEwayRedirect(
            $this->settings['eway-redirect-api-key'],
            $this->settings['eway-redirect-password'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Invoice payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\Invoice
     */
    protected function createInvoiceGateway()
    {
        return new Invoice();
    }

    /**
     * Create a new Mercanet (BNP Paribas) payment gateway
     *
     * @return OmnipayMercanet
     */
    protected function createMercanetGateway()
    {
        return new OmnipayMercanet(
            $this->settings['mercanet-merchant-id'],
            $this->settings['mercanet-secret-key'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new NAB Transact payment gateway
     *
     * @return NABTransact
     */
    protected function createNabTransactGateway()
    {
        return new NABTransact(
            $this->settings['nab-transact-merchant-id'],
            $this->settings['nab-transact-password'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Netbanx payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OptimalNetbanx
     */
    protected function createNetbanxGateway()
    {
        return new OptimalNetbanx(
            $this->settings['netbanx-store-id'], // API ID
            $this->settings['netbanx-store-password'], // API KEY
            $this->settings['netbanx-account-number'], // ACCOUNT NUMBER
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Payflow payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipayPayflow
     */
    protected function createPayflowGateway()
    {
        return new OmnipayPayflow(
            $this->settings['payflow-partner'],
            $this->settings['payflow-vendor'],
            $this->settings['payflow-username'],
            $this->settings['payflow-password'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new PaypalExpress payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipayPaypalExpress
     */
    protected function createPaypalexpressGateway()
    {
        return new OmnipayPaypalExpress(
            $this->settings['paypal-express-username'],
            $this->settings['paypal-express-password'],
            $this->settings['paypal-express-signature'],
            $this->settings['payment-test-mode'] ?? false
        );
    }

    /**
     * Create a new Sagepay payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipayRealex
     */
    protected function createRealexGateway()
    {
        return new OmnipayRealex(
            $this->settings['realex-merchant-id'],
            $this->settings['realex-account'],
            $this->settings['realex-shared-secret'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Sagepay payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipaySagepay
     */
    protected function createSagepayGateway()
    {
        return new OmnipaySagepay(
            $this->settings['sagepay-vendor'],
            $this->settings['sagepay-referrer-id'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Securepay payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipaySecurepay
     */
    protected function createSecurepayGateway()
    {
        return new OmnipaySecurepay(
            $this->settings['securepay-merchant-id'],
            $this->settings['securepay-transaction-password'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Stripe Connect payment gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\OmnipayStripeConnect
     *
     * @throws InvalidGatewayConfigException
     */
    protected function createStripeConnectGateway()
    {
        return new OmnipayStripeConnect(
            $this->settings['stripe-connect-user-id'] ?? '',
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Stripe Checkout (iDEAL) payment gateway
     *
     * @return Ideal
     *
     * @throws InvalidGatewayConfigException
     */
    protected function createIdealGateway()
    {
        return new Ideal(
            $this->settings['stripe-connect-user-id'] ?? '',
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Stripe Checkout (AliPay) payment gateway
     **
     * @throws InvalidGatewayConfigException
     */
    protected function createAliPayGateway()
    {
        return new Alipay(
            $this->settings['stripe-connect-user-id'] ?? '',
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new generic Stripe Checkout payment gateway
     **
     * @throws InvalidGatewayConfigException
     */
    protected function createStripeCheckoutGateway()
    {
        return new StripeCheckout(
            $this->settings['stripe-connect-user-id'] ?? '',
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new WestpacPayway gateway
     *
     * @return \AwardForce\Modules\Payments\Gateways\WestpacPayway
     */
    protected function createWestpacpaywayGateway()
    {
        return new WestpacPayway(
            $this->filesystem,
            $this->settings['westpac-payway-username'],
            $this->settings['westpac-payway-password'],
            $this->settings['westpac-payway-merchant-id'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new WorldPay payment gateway
     */
    protected function createWorldpayGateway()
    {
        return new OmnipayWorldpay(
            $this->settings['worldpay-installation-id'],
            '',
            '',
            '',
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new CCAvenue payment gateway
     */
    protected function createCCAvenueGateway()
    {
        return new CCAvenue(
            $this->settings['ccavenue-merchant-id'],
            $this->settings['ccavenue-access-code'],
            $this->settings['ccavenue-key'],
            $this->settings['payment-test-mode']
        );
    }

    /**
     * Create a new Paystack payment gateway
     *
     * @return \AwardForce\Modules\Payments\Contracts\Gateway
     */
    protected function createPaystackGateway()
    {
        return new PayStack(
            $this->settings['paystack-public-key'],
            $this->settings['paystack-secret-key'],
            $this->settings['paystack-merchant-email'],
            $this->settings['payment-test-mode']
        );
    }

    public static function isSecurePay(): bool
    {
        return setting('payment-gateway') === 'securepay';
    }

    public static function isEway(): bool
    {
        return setting('payment-gateway') === 'eway';
    }
}
