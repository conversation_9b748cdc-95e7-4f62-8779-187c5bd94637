<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Modules\Payments\Contracts\Gateway;
use AwardForce\Modules\Payments\Response;
use Eloquence\Behaviours\Slug;

class Invoice implements Gateway
{
    /**
     * @var string
     */
    protected $currency;

    /**
     * Get test mode
     *
     * @return bool
     */
    public function getTestMode()
    {
        return false;
    }

    /**
     * Set test mode
     *
     *
     * @return void
     */
    public function setTestMode($mode)
    {
    }

    /**
     * Get currency code
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set the currency code
     *
     * @param  string  $currency
     * @return void
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * Handle making the purchase
     *
     * @param  array  $data
     * @return \AwardForce\Modules\Payments\Contracts\Response
     */
    public function purchase($amount, $data = [])
    {
        return new Response(true, 'Success', $this->generateInvoiceReference(), $amount, $this->getCurrency());
    }

    /**
     * Return a random string to be used as an invoice reference
     *
     * @return string
     */
    protected function generateInvoiceReference()
    {
        return 'AF'.\Illuminate\Support\Str::random();
    }

    public function cart(array $requestData): ?Slug
    {
        return null;
    }

    public function validate(string $payload, array $headers): bool
    {
        return false;
    }
}
