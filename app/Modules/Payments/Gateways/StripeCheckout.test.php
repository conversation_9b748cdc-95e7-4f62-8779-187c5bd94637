<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Payments\GatewayManager;
use AwardForce\Modules\Payments\RedirectResponse;
use AwardForce\Modules\Payments\Response;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Consumer;
use Eloquence\Behaviours\Slug;
use Mockery as m;
use Stripe\ApiRequestor;
use Stripe\Event;
use Stripe\HttpClient\ClientInterface;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class StripeCheckoutTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private string $clientId;
    private ClientInterface $clientMock;

    public static function gateways()
    {
        return [['Alipay'], ['Ideal']];
    }

    public function init()
    {
        \Config::set('services.stripe_connect.api_key_test', 'sk_test_Z6eA6d0tScvNuV9fuelZ9kBo');
        app(SettingRepository::class)->saveSetting('payment-test-mode', true);
        app(SettingRepository::class)->saveSetting('stripe-connect-user-id', $this->clientId = 'connected-user-id');
        $membership = new Membership();
        $user = $this->muffin(User::class);
        $user->setRelation('currentMembership', $membership);
        Consumer::set(new UserConsumer($user));
        $this->clientMock = m::spy(ClientInterface::class);
        ApiRequestor::setHttpClient($this->clientMock);
    }

    /**
     * @dataProvider gateways
     */
    public function testItRedirectToStripeAfterPurchase(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        $amount = 13.95;
        $gateway->setCurrency($currency = 'CNY');
        $response = [
            'id' => 'FakeId',
            'url' => $stripeRedirectUrl = 'https://stripe.com/redirect',
        ];

        $this->clientMock->shouldReceive('request')->withArgs(function ($method, $url, $headers, $params) use ($amount, $currency) {
            $this->assertStringContainsString(config('services.stripe_connect.api_key_test'), collect($headers)->filter(fn($value) => \Str::contains($value, 'Bearer'))->first());
            $this->assertStringContainsString($this->clientId, collect($headers)->filter(fn($value) => \Str::contains($value, 'Stripe-Account'))->first());
            $this->assertEquals($amount * 100, $params['line_items'][0]['price_data']['unit_amount']);
            $this->assertEquals($currency, $params['line_items'][0]['price_data']['currency']);

            return true;
        })->andReturn([json_encode($response), 200, []]);

        $response = $gateway->purchase($amount);

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals($stripeRedirectUrl, $response->redirectUrl());
    }

    /**
     * @dataProvider gateways
     */
    public function testItReturnsASuccessResponseWhenSucceed(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        $gateway->setCurrency($currency = 'CNY');
        $gateway->setGatewayParameters([
            'type' => Event::PAYMENT_INTENT_SUCCEEDED,
            'data' => ['object' => [
                'latest_charge' => 'tx_123',
                'amount' => 1000,
            ]],
        ]);

        $response = $gateway->complete();

        $this->assertInstanceOf(Response::class, $response);
        $this->assertTrue($response->success());
        $this->assertEquals('tx_123', $response->transactionRef());
        $this->assertEquals(10, $response->amount());
        $this->assertEquals('CNY', $response->currency());
    }

    /**
     * @dataProvider gateways
     */
    public function testItReturnsAFailureResponseWhenUnsuccessful(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        $gateway->setGatewayParameters([
            'type' => Event::PAYMENT_INTENT_PAYMENT_FAILED,
            'data' => ['object' => [
                'last_payment_error' => ['message' => 'no bueno'],
            ]],
        ]);

        $response = $gateway->complete();

        $this->assertInstanceOf(Response::class, $response);
        $this->assertFalse($response->success());
        $this->assertEquals('no bueno', $response->message());
    }

    /**
     * @dataProvider gateways
     */
    public function testItUsesTheApiKeyBasedOnTheTestMode(string $gatewayName)
    {
        $settings = m::mock(SettingRepository::class);
        $settings
            ->shouldReceive('getAllAsKeyValue')
            ->andReturn(
                ['payment-test-mode' => true, 'stripe-connect-user-id' => $this->clientId],
                ['payment-test-mode' => false, 'stripe-connect-user-id' => $this->clientId]
            );
        app()->instance(SettingRepository::class, $settings);

        \Config::spy();
        \Config::shouldReceive('get')->once()->with('services.stripe_connect.api_key_test', null)->andReturn('test');
        \Config::shouldReceive('get')->once()->with('services.stripe_connect.api_key_live', null)->andReturn('live');

        GatewayManager::create($gatewayName);
        GatewayManager::create($gatewayName);
    }

    /**
     * @dataProvider gateways
     */
    public function testItReturnsErrorResponseWhenApiKeyIsNotValid(string $gatewayName)
    {
        \Config::set('services.stripe_connect.api_key_test', '');
        $gateway = GatewayManager::create($gatewayName);
        $gateway->setCurrency('CNY');
        $response = $gateway->purchase(10);

        $this->assertFalse($response->success());
    }

    /**
     * @dataProvider gateways
     */
    public function testExtractsCartIfAvailable(string $gatewayName)
    {
        $requestData = ['data' => ['object' => ['metadata' => ['cart' => 'some-cart']]]];

        $cart = GatewayManager::create($gatewayName)->cart($requestData);

        $this->assertEquals(new Slug('some-cart'), $cart);
    }

    /**
     * @dataProvider gateways
     */
    public function testDoesNotExtractCartIfNotAvailable(string $gatewayName)
    {
        $requestData = ['data' => ['object' => ['metadata' => ['cart' => 'some-cart']]]];

        $cart = GatewayManager::create($gatewayName)->cart($requestData);

        $this->assertEquals(new Slug('some-cart'), $cart);
    }
}
