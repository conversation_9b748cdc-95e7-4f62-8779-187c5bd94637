<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Modules\Accounts\Contracts\GlobalAccountRepository;
use AwardForce\Modules\Accounts\Models\GlobalAccount;

enum WebhookGateway: string
{
    case StripeCheckout = 'stripe-checkout';

    public function globalAccount(array $data, GlobalAccountRepository $globalAccounts): ?GlobalAccount
    {
        $globalAccountId = match ($this) {
            self::StripeCheckout => StripeCheckout::globalAccountId($data),
        };

        return $globalAccountId ? $globalAccounts->getById($globalAccountId) : null;
    }
}
