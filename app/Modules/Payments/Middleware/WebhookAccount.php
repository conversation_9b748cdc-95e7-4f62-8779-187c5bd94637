<?php

namespace AwardForce\Modules\Payments\Middleware;

use AwardForce\Library\Database\DatabaseSelector;
use AwardForce\Modules\Accounts\Contracts\GlobalAccountRepository;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\GlobalAccount;
use AwardForce\Modules\Payments\Gateways\WebhookGateway;
use Closure;
use Illuminate\Http\Request;

class WebhookAccount
{
    public function __construct(protected DatabaseSelector $databases, protected GlobalAccountRepository $globalAccounts)
    {
    }

    public function handle(Request $request, Closure $next): mixed
    {
        $gateway = WebhookGateway::tryFrom($request->route('gateway'));

        if (! ($globalAccount = $gateway->globalAccount($request->all(), $this->globalAccounts))) {
            return response()->json(['skipped' => 'account']);
        }

        if (! $this->inCurrentRegion($globalAccount)) {
            return response()->json(['skipped' => 'region']);
        }

        $this->databases->fromGlobalAccount($globalAccount);
        CurrentAccount::set($globalAccount->account);

        return $next($request);
    }

    /**
     * We only want to process webhook requests if the account exists in the current region. If not we can skip
     */
    private function inCurrentRegion(GlobalAccount $globalAccount)
    {
        return $globalAccount?->region->name() === config('awardforce.region');
    }
}
