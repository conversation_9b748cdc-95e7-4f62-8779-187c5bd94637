<?php

namespace AwardForce\Modules\Payments\Middleware;

use AwardForce\Modules\Accounts\Contracts\GlobalAccountRepository;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Payments\Gateways\StripeCheckout;
use Illuminate\Http\Request;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class WebhookAccountTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected StripeCheckout $gateway;
    protected GlobalAccountRepository $globalAccounts;
    protected Request $request;

    protected function init()
    {
        $this->request = $this->mock(Request::class);
        $this->request
            ->shouldReceive('route')->andReturn('stripe-checkout')
            ->shouldReceive('all')->andReturn([]);

        $this->globalAccounts = $this->mock(GlobalAccountRepository::class);
        $this->globalAccounts->shouldReceive('getById')->andReturnNull()->byDefault();

        $this->gateway = $this->mock('alias:'.StripeCheckout::class);
        $this->gateway->shouldReceive('globalAccountId')->andReturnNull()->byDefault();

        config()->set('awardforce.region', 'au');
    }

    public function testShouldSkipIfNoGlobalAccount()
    {
        $response = app(WebhookAccount::class)->handle($this->request, function () {
            throw new \Exception('This should not happen');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(['skipped' => 'account'], json_decode($response->getContent(), true));
    }

    public function testShouldSkipIfGlobalAccountInDifferentRegion()
    {
        $account = $this->muffin(Account::class, ['region' => 'ca']);
        $this->gateway->shouldReceive('globalAccountId')->andReturn($account->globalId);
        $this->globalAccounts->shouldReceive('getById')->andReturn($account->globalAccount);

        $response = app(WebhookAccount::class)->handle($this->request, function () {
            throw new \Exception('This should not happen');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(['skipped' => 'region'], json_decode($response->getContent(), true));
    }

    public function testShouldPassForwardIfGlobalAccountInSameRegion()
    {
        $this->gateway->shouldReceive('globalAccountId')->andReturn(current_account()->globalId);
        $this->globalAccounts->shouldReceive('getById')->andReturn(current_account()->globalAccount);

        $response = app(WebhookAccount::class)->handle($this->request, function () {
            return 'pass through';
        });

        $this->assertEquals('pass through', $response);
    }
}
