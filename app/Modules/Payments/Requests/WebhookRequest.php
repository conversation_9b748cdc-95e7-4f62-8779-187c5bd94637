<?php

namespace AwardForce\Modules\Payments\Requests;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Payments\Contracts\Gateway;
use AwardForce\Modules\Payments\GatewayManager;
use Eloquence\Behaviours\Slug;

class WebhookRequest extends FormRequest
{
    protected Gateway $gatewayInstance;

    public function authorize()
    {
        return $this->gateway()->validate($this->getContent(), $this->headers->all());
    }

    public function cartSlug(): Slug
    {
        return $this->gateway()->cart($this->all());
    }

    protected function gateway(): Gateway
    {
        return $this->gatewayInstance ??= app(GatewayManager::class)->create($this->route('gateway'));
    }

    protected function failedAuthorization()
    {
        abort(response()->json([], 403));
    }
}
