<?php

namespace AwardForce\Modules\ReviewFlow\Listeners;

use AwardForce\Library\Database\Firebase\Database as Firebase;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\ReviewFlow\Commands\CreateEntrySubmissionReviewTaskCommand;
use AwardForce\Modules\ReviewFlow\Commands\CreateNextStageReviewTasksCommand;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Support\Facades\Bus;
use Platform\Events\EventDispatcher;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ReviewFlowListenerTest extends BaseTestCase
{
    use Database;
    use EventDispatcher;
    use Laravel;

    public function init()
    {
        $this->spy(Firebase::class);
    }

    private function createTask(Entry $entry)
    {
        return ReviewTask::generate(
            $entry->id,
            ($stage = $this->muffin(ReviewStage::class))->id,
            'manager',
            current_account_id()
        );
    }

    public function testCreatesReviewTaskWhenEntryWasSubmitted(): void
    {
        Bus::fake(CreateEntrySubmissionReviewTaskCommand::class);
        $this->setupUserWithRole('Program Manager', true);
        $this->createTask($entry = $this->muffin(Entry::class));

        $entry->submit();
        $this->dispatch($entry->releaseEvents());

        Bus::assertDispatched(CreateEntrySubmissionReviewTaskCommand::class);
    }

    public function testCreatesReviewTaskWhenEntryWasSubmittedDifferentForm(): void
    {
        /** @var Form $form */
        $form = $this->muffin(Form::class);

        $category1 = $this->muffin(Category::class);
        $category2 = $this->muffin(Category::class);

        $form->categories()->save($category1);

        $reviewStage = $this->muffin(ReviewStage::class, [
            'form_id' => $form->id,
            'proceed_approve' => 0,
            'stop_reject' => 0,
            'start_on_submit' => true,
            'category_option' => 'all',
            'review_by' => ReviewStage::REVIEW_MANAGER,
        ]);

        $entry1 = $this->muffin(Entry::class, ['form_id' => $form->id, 'category_id' => $category1->id]);
        $entry2 = $this->muffin(Entry::class, ['form_id' => $form->id, 'category_id' => $category2->id]);

        $entry1->submit();
        $entry2->submit();
        $this->dispatch($entry1->releaseEvents());
        $this->dispatch($entry2->releaseEvents());

        $this->assertCount(1, app(ReviewTaskRepository::class)->getForEntrySorted($entry1->id));
        $this->assertCount(0, app(ReviewTaskRepository::class)->getForEntrySorted($entry2->id));
    }

    public function testDoesNotCreateReviewTaskWhenEntryWasSubmittedInArchivedSeason(): void
    {
        Bus::fake(CreateEntrySubmissionReviewTaskCommand::class);
        $season = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $this->createTask($entry = $this->muffin(Entry::class, ['season_id' => $season->id]));

        $entry->submit();
        $this->dispatch($entry->releaseEvents());
        Bus::assertNotDispatched(CreateEntrySubmissionReviewTaskCommand::class);
    }

    public function testCreatesReviewTaskWhenTaskWasProceeded(): void
    {
        Bus::fake(CreateNextStageReviewTasksCommand::class);
        $task = $this->createTask($entry = $this->muffin(Entry::class));
        $task->proceed();

        $this->dispatch($task->releaseEvents());

        Bus::assertDispatched(CreateNextStageReviewTasksCommand::class);
    }

    public function testDoesNotCreateReviewTaskWhenTaskWasProceededAndSeasonIsArchived(): void
    {
        Bus::fake(CreateNextStageReviewTasksCommand::class);
        $season = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $task = $this->createTask($entry = $this->muffin(Entry::class, ['season_id' => $season->id]));

        $task->proceed();

        $this->dispatch($task->releaseEvents());
        Bus::assertNotDispatched(CreateNextStageReviewTasksCommand::class);
    }

    public function testCreatesReviewTaskWhenTaskWasStopped(): void
    {
        Bus::fake(CreateNextStageReviewTasksCommand::class);

        $task = $this->createTask($entry = $this->muffin(Entry::class));
        $task->stop();

        $this->dispatch($task->releaseEvents());

        Bus::assertDispatched(CreateNextStageReviewTasksCommand::class);
    }

    public function testDoesNotCreateReviewTaskWhenTaskWasStoppedAndSeasonIsArchived(): void
    {
        Bus::fake(CreateNextStageReviewTasksCommand::class);
        $season = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $task = $this->createTask($entry = $this->muffin(Entry::class, ['season_id' => $season->id]));
        $task->stop();

        $this->dispatch($task->releaseEvents());
        Bus::assertNotDispatched(CreateNextStageReviewTasksCommand::class);
    }

    public function testItShouldCreateReviewTaskWhenFeatureEnabled(): void
    {
        Bus::fake(CreateEntrySubmissionReviewTaskCommand::class);
        Feature::shouldReceive('enabled')->once()->with('review_flow')->andReturn(true);
        // TODO: uncomment after the TriggerAIFieldValueGeneration listener is wired up
        //        Feature::shouldReceive('enabled')->once()->with('ai_agents')->andReturnFalse();
        $entry = $this->muffin(Entry::class);
        $entry->submit();

        $this->dispatch($entry->releaseEvents());

        Bus::assertDispatched(CreateEntrySubmissionReviewTaskCommand::class);
    }

    public function testItShouldNotCreateReviewTaskWhenFeatureDisabled(): void
    {
        Bus::fake(CreateNextStageReviewTasksCommand::class);
        Feature::shouldReceive('enabled')->once()->with('review_flow')->andReturn(false);
        // TODO: uncomment after the TriggerAIFieldValueGeneration listener is wired up
        //        Feature::shouldReceive('enabled')->once()->with('ai_agents')->andReturnFalse();
        $entry = $this->muffin(Entry::class);
        $entry->submit();

        $this->dispatch($entry->releaseEvents());

        Bus::assertNotDispatched(CreateNextStageReviewTasksCommand::class);
    }
}
