<template>
	<modal
		v-if="visible"
		ref="modal"
		v-model="isModalOpen"
		style="position: relative"
		:header="false"
		:footer="true"
		:close-with-escape="false"
		:confirm-on-enter="false"
		:close-on-confirm="false"
		:close-on-backdrop-clicks="false"
		:confirm-button="true"
		:confirm-disabled="!ready"
		:confirm-button-label="lang.get('users.buttons.send_invite')"
		:close-button="true"
		:close-button-label="lang.get('buttons.cancel')"
		@backdrop-clicked="onClose"
		@closed="onClose"
		@confirmed="onInvite"
	>
		<close-icon slot="before-content" @click.prevent.stop="onClose" />

		<h4 class="modal-title">{{ lang.get('collaboration.modal.header-invite') }}</h4>

		<div v-if="loading" slot="before-content" class="overlay">
			<i class="af-icons af-icons-repeat af-icons-animate-rotate"></i>
		</div>

		<form class="collaborators-form">
			<validation-errors />

			<emails-box
				:key="componentKey"
				:placeholder="lang.get('collaboration.form.emails.placeholder')"
				@change="formCallbacks.onEmails"
			/>

			<div class="form-group">
				<label for="collaborator-emails" v-text="lang.get('collaboration.form.privilege.label')"></label>
				<select-field
					class="privileges"
					:value="form.privilege"
					:items="privileges"
					:aria-label="lang.get('collaboration.form.privilege.label')"
					@selected="formCallbacks.onPrivilege"
				></select-field>
			</div>

			<div class="form-group">
				<label for="collaborator-emails">
					{{ lang.get('collaboration.form.message.label') }}
					{{ lang.get('miscellaneous.optional') }}
				</label>
				<text-editor
					id="message"
					v-model="form.message"
					name="message"
					value-property="name"
					id-property="id"
					:allows-inline-images="true"
					:remove-items-toolbar="['mediaEmbed']"
				/>
			</div>
		</form>
	</modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import EmailsBox from '@/lib/components/Shared/EmailsBox.vue';
import TextEditor from '@/lib/components/Shared/editor/TextEditor.vue';
import { useController } from '@/domain/services/Composer';
import { collaboratorsInviteModalController, Props, View } from './CollaboratorsInviteModal.controller';
import { Modal, SelectField } from 'vue-bootstrap';
import CloseIcon from '@/lib/components/ListActions/Partials/CloseIcon.vue';
import ValidationErrors from '@/lib/components/Shared/ValidationErrors.vue';

export default defineComponent<Props, View>({
	components: {
		ValidationErrors,
		Modal,
		TextEditor,
		SelectField,
		EmailsBox,
		CloseIcon,
	},
	props: {},
	setup: useController(collaboratorsInviteModalController, 'collaboratorsInviteModalController') as () => View,
});
</script>

<!-- eslint-disable max-len -->
<style lang="scss">
.collaborators-form {
	padding-top: 20px;
}

.privileges::-ms-expand {
	display: none;
}

.privileges {
	-webkit-appearance: none;
	-moz-appearance: none;
	background: transparent;
	background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
	background-repeat: no-repeat;
	background-position-x: 100%;
	background-position-y: 5px;
}

.collaborators {
	padding-top: 20px;

	.collaborator {
		display: grid;
		grid-template-columns: 30px 2fr 1fr;

		&:not(:last-child) {
			margin-bottom: 20px;
		}

		.collaborator-actions {
			display: flex;

			.dropdown {
				display: flex;
				align-items: center;
			}
		}

		.collaborator-info {
			display: grid;
			grid-template-columns: 50px 100%;
			grid-gap: 10px;

			.collaborator-name {
				font-weight: bold;
			}
		}

		.collaborator-privilege {
			display: flex;
			align-items: center;
		}
	}
}

.dropdown-toggle-light {
	text-decoration: none;
	display: flex;
	align-items: baseline;
	color: black;
}

.modal-dialog-loading {
	.overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.modal-content {
		opacity: 0.9;
	}
}
</style>
