import tectoastr from '@/lib/tectoastr';
import { useFileSelector } from '@/lib/components/Uploader/FileSelector/useFileSelector';
import { computed, ComputedRef, onMounted, Ref, ref } from 'vue';
import { FileSelectorEmitters, FileSelectorEvents } from '@/lib/components/Uploader/FileSelector/FileSelectorEmitter';
import { Trans, trans } from '@/domain/dao/Translations';

type Props = {
	isUploading: Ref<boolean>;
	showFileDialog: boolean;
	acceptedFileTypes: string;
};

type View = {
	lang: Trans;
	openFileDialog: () => void;
	handleFileChange: (event: Event) => void;
	dragAndDropElement: Ref<HTMLDivElement | null>;
	isDragging: Ref<boolean>;
	dragDrop: (event: DragEvent) => Promise<void>;
	dragLeave: (event: DragEvent) => void;
	dragOver: (event: DragEvent) => void;
	classes: ComputedRef<Record<string, boolean>>;
	fileInput: Ref<HTMLInputElement | null>;
	acceptedFileTypes: Ref<string>;
};

type Ctx = {
	emit: FileSelectorEmitters;
};

const fileSelectorController = (props: Props, { emit }: Ctx): View => {
	const lang = trans();
	const fileInput = ref<HTMLInputElement | null>(null);
	const dragAndDropElement: Ref<HTMLDivElement | null> = ref(null);
	const onDrop = (files: FileList | never[]) => {
		handleFiles(files);
	};

	const { isDragging, dragDrop, dragLeave, dragOver } = useFileSelector(dragAndDropElement, onDrop);
	const classes = computed(() => ({
		'action-card': true,
		'full-width': true,
		'drag-enter': isDragging.value && !props.isUploading.value,
	}));
	const openFileDialog = () => {
		if (fileInput.value) {
			fileInput.value.click();
		}
	};

	const handleFiles = async (files: FileList | never[]) => {
		if (files.length > 1) {
			tectoastr.error(lang.get('files.messages.single_file_limit'));
			return;
		}

		const file: File = files[0];
		emit(FileSelectorEvents.Selected, file);
	};

	const handleFileChange = async (event: Event) => {
		if (props.isUploading.value) {
			return;
		}

		if (!(event.target instanceof HTMLInputElement)) return;

		const files = event.target.files || [];

		handleFiles(files);
	};

	const acceptedFileTypes = ref(props.acceptedFileTypes || '*');

	onMounted(() => {
		if (props.showFileDialog) {
			openFileDialog();
		}
	});
	return {
		lang,
		openFileDialog,
		handleFileChange,
		dragAndDropElement,
		isDragging,
		dragDrop,
		dragLeave,
		dragOver,
		classes,
		fileInput,
		acceptedFileTypes,
	};
};

export { fileSelectorController, Props, View };
