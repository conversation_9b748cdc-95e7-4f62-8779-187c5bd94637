import { Composer } from '@/domain/services/Composer';
import { expect } from 'chai';
import FileSelector from '@/lib/components/Uploader/FileSelector/FileSelector.vue';
import LogoUploader from '@/modules/organisations/components/logo/LogoUploader.vue';
import { shallowMount } from '@vue/test-utils';

const defaultProps = {
	organisation: {},
	currentImage: {},
	uploadOptions: { routes: {} },
};

const baseView = {};

describe('LogoUploader', () => {
	it('should accept image files', () => {
		Composer.mockView('OrganisationLogoController', { ...baseView });

		const organisationLogo = shallowMount(LogoUploader, { propsData: { ...defaultProps } });

		const fileSelector = organisationLogo.findComponent(FileSelector);

		expect(fileSelector.exists()).to.be.true;
		expect(fileSelector.props('acceptedFileTypes')).to.equal('image/*');
	});
});
